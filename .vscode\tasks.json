{"version": "2.0.0", "tasks": [{"label": "Start React Native Metro", "type": "shell", "command": "npx react-native start", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Build Android Debug", "type": "shell", "command": "cd android && .\\gradlew assembleDebug", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Build Android Release", "type": "shell", "command": "cd android && .\\gradlew assembleRelease", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Install Android Debug", "type": "shell", "command": "adb install android\\app\\build\\outputs\\apk\\debug\\app-debug.apk", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "dependsOn": "Build Android Debug"}, {"label": "Start Server", "type": "shell", "command": "cd server && npm start", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Clean Android", "type": "shell", "command": "cd android && .\\gradlew clean", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "npm install --legacy-peer-deps", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Complete Build and Install", "dependsOrder": "sequence", "dependsOn": ["Install Dependencies", "Build Android Debug", "Install Android Debug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}