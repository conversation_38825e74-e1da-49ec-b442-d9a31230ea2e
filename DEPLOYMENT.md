# Deployment Guide

## Server Deployment

### Option 1: VPS/Cloud Server

1. **Set up a VPS** (DigitalOcean, AWS, etc.)
2. **Install Node.js**:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **Deploy server**:
   ```bash
   git clone <your-repo>
   cd server
   npm install
   npm start
   ```

4. **Set up SSL** (using Let's Encrypt):
   ```bash
   sudo apt install certbot
   sudo certbot certonly --standalone -d your-domain.com
   ```

5. **Configure reverse proxy** (nginx):
   ```nginx
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### Option 2: Heroku

1. **Create Heroku app**:
   ```bash
   heroku create your-app-name
   ```

2. **Deploy**:
   ```bash
   cd server
   git init
   git add .
   git commit -m "Initial commit"
   heroku git:remote -a your-app-name
   git push heroku main
   ```

## Mobile App Building

### Android APK

1. **Generate signing key**:
   ```bash
   keytool -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **Configure gradle** (`android/app/build.gradle`):
   ```gradle
   android {
       ...
       signingConfigs {
           release {
               storeFile file('my-upload-key.keystore')
               storePassword 'your-password'
               keyAlias 'my-key-alias'
               keyPassword 'your-password'
           }
       }
       buildTypes {
           release {
               signingConfig signingConfigs.release
               minifyEnabled enableProguardInReleaseBuilds
               proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
           }
       }
   }
   ```

3. **Build APK**:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

### iOS IPA

1. **Configure signing** in Xcode
2. **Archive and export** for distribution
3. **For enterprise distribution**: Use enterprise certificate

## Security Hardening

### Server Security

1. **Firewall configuration**:
   ```bash
   sudo ufw allow ssh
   sudo ufw allow 443
   sudo ufw enable
   ```

2. **Environment variables**:
   ```bash
   export ENCRYPTION_KEY="your-32-byte-key"
   export JWT_SECRET="your-jwt-secret"
   ```

3. **Rate limiting**:
   ```javascript
   const rateLimit = require('express-rate-limit');
   app.use(rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100 // limit each IP to 100 requests per windowMs
   }));
   ```

### App Security

1. **Obfuscation**: Use code obfuscation tools
2. **Certificate pinning**: Pin SSL certificates
3. **Root detection**: Implement root/jailbreak detection

## Testing Checklist

### Android Testing

- [ ] App installs successfully
- [ ] Setup screen appears on first launch
- [ ] Target mode hides app from launcher
- [ ] Background service starts and persists
- [ ] App survives device reboot
- [ ] Controller can connect to targets
- [ ] Commands execute successfully
- [ ] Encrypted communication works

### iOS Testing

- [ ] App installs successfully
- [ ] Setup screen appears on first launch
- [ ] Controller mode works properly
- [ ] Background operation (limited)
- [ ] Communication with server works

### Server Testing

- [ ] WebSocket connections work
- [ ] Device registration works
- [ ] Command routing works
- [ ] Encryption/decryption works
- [ ] Multiple devices supported
- [ ] SSL/TLS properly configured

## Monitoring and Maintenance

### Server Monitoring

1. **Process manager** (PM2):
   ```bash
   npm install -g pm2
   pm2 start server.js --name stealth-server
   pm2 startup
   pm2 save
   ```

2. **Log monitoring**:
   ```bash
   pm2 logs stealth-server
   ```

3. **Health checks**:
   ```bash
   curl https://your-domain.com/health
   ```

### App Updates

1. **Over-the-air updates**: Implement CodePush for React Native
2. **Silent updates**: Update without user interaction
3. **Rollback capability**: Ability to revert problematic updates

## Legal Compliance

### Documentation Required

1. **Terms of Service**
2. **Privacy Policy**
3. **User Consent Forms**
4. **Legal Authorization Documentation**

### Compliance Checklist

- [ ] GDPR compliance (if applicable)
- [ ] Local privacy laws compliance
- [ ] Proper user consent mechanisms
- [ ] Data retention policies
- [ ] Audit logging

## Troubleshooting

### Common Issues

1. **App not hiding on Android**:
   - Check permissions
   - Verify native module linking
   - Test on different Android versions

2. **Background service stops**:
   - Disable battery optimization
   - Check manufacturer-specific settings
   - Implement service restart logic

3. **Connection issues**:
   - Verify server accessibility
   - Check firewall settings
   - Test with different networks

4. **iOS limitations**:
   - Use enterprise distribution
   - Consider alternative approaches
   - Document limitations clearly

### Debug Commands

```bash
# Check Android logs
adb logcat | grep StealthRemoteApp

# Check server logs
pm2 logs stealth-server

# Test server connectivity
curl -I https://your-domain.com/health

# Check app permissions
adb shell dumpsys package com.stealthremoteapp
```

## Support and Maintenance

### Regular Tasks

1. **Security updates**: Keep dependencies updated
2. **Certificate renewal**: Renew SSL certificates
3. **Log rotation**: Manage log file sizes
4. **Performance monitoring**: Monitor server performance
5. **Backup**: Regular backup of configurations

### Emergency Procedures

1. **Server downtime**: Backup server procedures
2. **Security breach**: Incident response plan
3. **App detection**: Mitigation strategies
4. **Legal issues**: Legal response procedures
