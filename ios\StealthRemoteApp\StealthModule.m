#import "StealthModule.h"
#import <React/RCTLog.h>

@implementation StealthModule

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(hideFromLauncher:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // iOS has limited hiding capabilities due to App Store restrictions
    // This would require enterprise distribution or jailbreak
    RCTLogInfo(@"iOS hiding attempted (limited functionality)");
    resolve(@"iOS hiding attempted (limited functionality)");
}

RCT_EXPORT_METHOD(showInLauncher:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // iOS restore functionality
    RCTLogInfo(@"iOS restore attempted");
    resolve(@"iOS restore attempted");
}

RCT_EXPORT_METHOD(hideFromRecents:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // iOS has limited recents hiding capabilities
    RCTLogInfo(@"iOS hide from recents attempted");
    resolve(@"iOS hide from recents attempted");
}

RCT_EXPORT_METHOD(isHiddenFromLauncher:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // Check if app is hidden (always false on iOS due to restrictions)
    resolve(@NO);
}

RCT_EXPORT_METHOD(enableStealthMode:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // iOS stealth mode (limited functionality)
    RCTLogInfo(@"iOS stealth mode enabled (limited functionality)");
    resolve(@"iOS stealth mode enabled (limited functionality)");
}

RCT_EXPORT_METHOD(disableStealthMode:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // iOS stealth mode disable
    RCTLogInfo(@"iOS stealth mode disabled");
    resolve(@"iOS stealth mode disabled");
}

@end
