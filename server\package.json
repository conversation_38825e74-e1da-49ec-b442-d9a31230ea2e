{"name": "stealth-remote-server", "version": "1.0.0", "description": "Secure server for stealth remote access app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["remote", "stealth", "websocket", "secure"], "author": "", "license": "ISC"}