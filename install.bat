@echo off
echo Installing Stealth Remote App...
echo.

echo Step 1: Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error installing dependencies!
    pause
    exit /b 1
)

echo.
echo Step 2: Setting up Android environment...
cd android
call gradlew clean
if %errorlevel% neq 0 (
    echo Error cleaning Android project!
    pause
    exit /b 1
)

echo.
echo Step 3: Building APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo Error building APK!
    pause
    exit /b 1
)

cd ..
echo.
echo ✅ Build completed successfully!
echo.
echo APK location: android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo To install on your phone:
echo 1. Enable Developer Options on your phone
echo 2. Enable USB Debugging
echo 3. Connect your phone via USB
echo 4. Run: adb install android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo Or copy the APK file to your phone and install manually.
echo.
pause
