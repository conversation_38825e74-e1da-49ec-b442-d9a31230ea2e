import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  DeviceEventEmitter,
  StatusBar,
  TextInput,
  Modal,
} from 'react-native';
import RemoteControlService from '../services/RemoteControlService';

interface DeviceConnection {
  id: string;
  name: string;
  platform: string;
  lastSeen: string;
  status: 'online' | 'offline';
}

const ControllerScreen: React.FC = () => {
  const [connectedDevices, setConnectedDevices] = useState<DeviceConnection[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<DeviceConnection | null>(null);
  const [commandResponse, setCommandResponse] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showCommandModal, setShowCommandModal] = useState(false);
  const [customCommand, setCustomCommand] = useState('');

  useEffect(() => {
    setupEventListeners();
    loadConnectedDevices();

    return () => {
      // Cleanup listeners
    };
  }, []);

  const setupEventListeners = () => {
    const devicesListener = DeviceEventEmitter.addListener(
      'devicesUpdated',
      (devices: DeviceConnection[]) => {
        setConnectedDevices(devices);
      }
    );

    const responseListener = DeviceEventEmitter.addListener(
      'commandResponse',
      (response: any) => {
        setCommandResponse(response);
        setIsLoading(false);
      }
    );

    return () => {
      devicesListener.remove();
      responseListener.remove();
    };
  };

  const loadConnectedDevices = async () => {
    try {
      const devices = await RemoteControlService.getConnectedDevices();
      setConnectedDevices(devices);
    } catch (error) {
      console.error('Failed to load devices:', error);
    }
  };

  const sendCommand = async (type: string, payload?: any) => {
    if (!selectedDevice) {
      Alert.alert('Error', 'Please select a device first');
      return;
    }

    setIsLoading(true);
    setCommandResponse(null);

    try {
      await RemoteControlService.sendCommand(selectedDevice.id, {
        type: type as any,
        payload,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      Alert.alert('Error', `Failed to send command: ${error.message}`);
      setIsLoading(false);
    }
  };

  const handleCustomCommand = () => {
    if (!customCommand.trim()) {
      Alert.alert('Error', 'Please enter a command');
      return;
    }

    sendCommand('custom', {command: customCommand});
    setShowCommandModal(false);
    setCustomCommand('');
  };

  const renderDevice = ({item}: {item: DeviceConnection}) => (
    <TouchableOpacity
      style={[
        styles.deviceItem,
        selectedDevice?.id === item.id && styles.selectedDevice,
      ]}
      onPress={() => setSelectedDevice(item)}>
      <View style={styles.deviceInfo}>
        <Text style={styles.deviceName}>{item.name}</Text>
        <Text style={styles.deviceDetails}>
          {item.platform} • {item.status}
        </Text>
        <Text style={styles.deviceLastSeen}>
          Last seen: {new Date(item.lastSeen).toLocaleString()}
        </Text>
      </View>
      <View style={[
        styles.statusIndicator,
        {backgroundColor: item.status === 'online' ? '#00FF00' : '#FF0000'}
      ]} />
    </TouchableOpacity>
  );

  const renderCommandButton = (title: string, command: string, color: string = '#007AFF') => (
    <TouchableOpacity
      style={[styles.commandButton, {backgroundColor: color}]}
      onPress={() => sendCommand(command)}
      disabled={isLoading || !selectedDevice}>
      <Text style={styles.commandButtonText}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Remote Controller</Text>
        <Text style={styles.subtitle}>
          {connectedDevices.length} device(s) available
        </Text>
      </View>

      <View style={styles.devicesSection}>
        <Text style={styles.sectionTitle}>Connected Devices</Text>
        <FlatList
          data={connectedDevices}
          renderItem={renderDevice}
          keyExtractor={(item) => item.id}
          style={styles.devicesList}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {selectedDevice && (
        <View style={styles.controlSection}>
          <Text style={styles.sectionTitle}>
            Control: {selectedDevice.name}
          </Text>
          
          <View style={styles.commandGrid}>
            {renderCommandButton('Get Info', 'getInfo', '#007AFF')}
            {renderCommandButton('Get Status', 'getStatus', '#34C759')}
            {renderCommandButton('Screenshot', 'screenshot', '#FF9500')}
            {renderCommandButton('Location', 'location', '#FF3B30')}
            {renderCommandButton('Files', 'files', '#5856D6')}
            {renderCommandButton('AnyDesk Status', 'anydesk-status', '#00CED1')}
            {renderCommandButton('Install AnyDesk', 'anydesk-install', '#32CD32')}
            {renderCommandButton('Launch AnyDesk', 'anydesk-launch', '#FF6347')}
            {renderCommandButton('AnyDesk ID', 'anydesk-id', '#9370DB')}
            {renderCommandButton('Config AnyDesk', 'anydesk-config', '#FFD700')}

            <TouchableOpacity
              style={[styles.commandButton, {backgroundColor: '#8E8E93'}]}
              onPress={() => setShowCommandModal(true)}
              disabled={isLoading}>
              <Text style={styles.commandButtonText}>Custom</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {commandResponse && (
        <View style={styles.responseSection}>
          <Text style={styles.sectionTitle}>Response</Text>
          <View style={styles.responseContainer}>
            <Text style={styles.responseText}>
              {JSON.stringify(commandResponse, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <Text style={styles.loadingText}>Sending command...</Text>
        </View>
      )}

      <Modal
        visible={showCommandModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCommandModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Custom Command</Text>
            <TextInput
              style={styles.modalInput}
              value={customCommand}
              onChangeText={setCustomCommand}
              placeholder="Enter custom command"
              placeholderTextColor="#666"
              multiline
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowCommandModal(false)}>
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.sendButton]}
                onPress={handleCustomCommand}>
                <Text style={styles.modalButtonText}>Send</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#CCCCCC',
  },
  devicesSection: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 15,
  },
  devicesList: {
    maxHeight: 200,
  },
  deviceItem: {
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333333',
  },
  selectedDevice: {
    borderColor: '#007AFF',
    backgroundColor: '#001122',
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  deviceDetails: {
    fontSize: 12,
    color: '#CCCCCC',
    marginBottom: 2,
  },
  deviceLastSeen: {
    fontSize: 10,
    color: '#888888',
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  controlSection: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#333333',
  },
  commandGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  commandButton: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  commandButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  responseSection: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#333333',
    maxHeight: 200,
  },
  responseContainer: {
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    padding: 15,
  },
  responseText: {
    color: '#00FF00',
    fontSize: 12,
    fontFamily: 'monospace',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 20,
    width: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalInput: {
    backgroundColor: '#333333',
    borderRadius: 8,
    padding: 15,
    color: '#FFFFFF',
    fontSize: 14,
    minHeight: 100,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#8E8E93',
  },
  sendButton: {
    backgroundColor: '#007AFF',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ControllerScreen;
