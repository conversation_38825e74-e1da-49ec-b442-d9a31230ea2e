@echo off
echo ========================================
echo    Stealth Remote App - Complete Build
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed!
    echo Please install Java JDK 11 or higher
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

:: Step 1: Install dependencies
echo 📦 Step 1: Installing dependencies...
call npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies!
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully
echo.

:: Step 2: Install server dependencies
echo 🖥️ Step 2: Installing server dependencies...
cd server
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install server dependencies!
    pause
    exit /b 1
)
cd ..
echo ✅ Server dependencies installed successfully
echo.

:: Step 3: Clean Android build
echo 🧹 Step 3: Cleaning Android build...
cd android
call gradlew clean
if %errorlevel% neq 0 (
    echo ❌ Failed to clean Android build!
    pause
    exit /b 1
)
echo ✅ Android build cleaned successfully
echo.

:: Step 4: Build Android APK
echo 🔨 Step 4: Building Android APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ❌ Failed to build Android APK!
    pause
    exit /b 1
)
cd ..
echo ✅ Android APK built successfully
echo.

:: Step 5: Check if APK exists
set APK_PATH=android\app\build\outputs\apk\debug\app-debug.apk
if not exist "%APK_PATH%" (
    echo ❌ APK file not found at %APK_PATH%
    pause
    exit /b 1
)

echo ✅ APK file created: %APK_PATH%
echo.

:: Step 6: Check for connected device
echo 📱 Step 6: Checking for connected Android device...
adb devices | findstr "device" | findstr -v "List" >nul
if %errorlevel% equ 0 (
    echo ✅ Android device detected
    echo.
    echo 🚀 Installing APK on device...
    adb install -r "%APK_PATH%"
    if %errorlevel% equ 0 (
        echo ✅ APK installed successfully on device!
    ) else (
        echo ⚠️ Failed to install APK automatically
        echo You can install manually by copying the APK to your phone
    )
) else (
    echo ⚠️ No Android device connected via USB
    echo.
    echo 📋 Manual installation instructions:
    echo 1. Copy %APK_PATH% to your phone
    echo 2. Enable "Unknown Sources" in Android settings
    echo 3. Install the APK file
)

echo.
echo ========================================
echo           BUILD COMPLETE! ✅
echo ========================================
echo.
echo 📱 APK Location: %APK_PATH%
echo 🖥️ Server: Run 'start-server.bat' to start
echo 🌐 Mobile Web: http://YOUR_IP:3000/mobile
echo.
echo 🎯 Next Steps:
echo 1. Start the server: start-server.bat
echo 2. Install APK on your phone
echo 3. Open app and select Target/Controller mode
echo 4. Use server URL: ws://YOUR_IP:3000
echo.
pause
