{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "emmet.includeLanguages": {"javascript": "javascriptreact"}, "files.associations": {"*.js": "javascriptreact"}, "search.exclude": {"**/node_modules": true, "**/android/build": true, "**/android/app/build": true, "**/ios/build": true, "**/.git": true}, "files.exclude": {"**/node_modules": true, "**/android/build": true, "**/android/app/build": true, "**/ios/build": true}, "react-native-tools.showUserTips": false, "react-native-tools.projectRoot": "./", "java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic"}