{"name": "StealthRemoteApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build-android": "cd android && gradlew assembleDebug"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "react-native-device-info": "^10.9.0", "react-native-encrypted-storage": "^4.0.3", "socket.io-client": "^4.7.2", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "^3.25.0", "react-native-safe-area-context": "^4.7.2", "react-native-gesture-handler": "^2.12.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}