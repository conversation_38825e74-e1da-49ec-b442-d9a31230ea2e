package com.stealthremoteapp;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

public class HiddenActivity extends Activity {
    private static final String TAG = "HiddenActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "HiddenActivity created");

        // This activity is used for secret access to the hidden app
        // It immediately launches the main activity and finishes itself
        
        try {
            Intent mainIntent = new Intent(this, MainActivity.class);
            mainIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mainIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            mainIntent.putExtra("hidden_access", true);
            
            startActivity(mainIntent);
            finish();
            
            Log.d(TAG, "Main activity launched from hidden access");
        } catch (Exception e) {
            Log.e(TAG, "Failed to launch main activity from hidden access", e);
            finish();
        }
    }
}
