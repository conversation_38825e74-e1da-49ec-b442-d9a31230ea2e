package com.stealthremoteapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;

public class BackgroundServiceModule extends ReactContextBaseJavaModule {
    private static final String TAG = "BackgroundService";
    private static final String CHANNEL_ID = "stealth_service_channel";
    private ReactApplicationContext reactContext;

    public BackgroundServiceModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return "BackgroundService";
    }

    @ReactMethod
    public void start(ReadableMap options, Promise promise) {
        try {
            createNotificationChannel();
            
            Intent serviceIntent = new Intent(reactContext, StealthBackgroundService.class);
            if (options.hasKey("taskName")) {
                serviceIntent.putExtra("taskName", options.getString("taskName"));
            }
            if (options.hasKey("taskTitle")) {
                serviceIntent.putExtra("taskTitle", options.getString("taskTitle"));
            }
            if (options.hasKey("taskDesc")) {
                serviceIntent.putExtra("taskDesc", options.getString("taskDesc"));
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                reactContext.startForegroundService(serviceIntent);
            } else {
                reactContext.startService(serviceIntent);
            }

            Log.d(TAG, "Background service started");
            promise.resolve("Background service started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start background service", e);
            promise.reject("SERVICE_ERROR", "Failed to start background service: " + e.getMessage());
        }
    }

    @ReactMethod
    public void stop(Promise promise) {
        try {
            Intent serviceIntent = new Intent(reactContext, StealthBackgroundService.class);
            reactContext.stopService(serviceIntent);
            
            Log.d(TAG, "Background service stopped");
            promise.resolve("Background service stopped");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop background service", e);
            promise.reject("SERVICE_ERROR", "Failed to stop background service: " + e.getMessage());
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                CHANNEL_ID,
                "Stealth Service Channel",
                NotificationManager.IMPORTANCE_LOW
            );
            serviceChannel.setDescription("Background service for stealth app");
            serviceChannel.setShowBadge(false);
            serviceChannel.setSound(null, null);
            serviceChannel.enableVibration(false);

            NotificationManager manager = reactContext.getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }

    public static class StealthBackgroundService extends Service {
        private static final int NOTIFICATION_ID = 1001;

        @Override
        public void onCreate() {
            super.onCreate();
            Log.d(TAG, "StealthBackgroundService created");
        }

        @Override
        public int onStartCommand(Intent intent, int flags, int startId) {
            String taskTitle = intent.getStringExtra("taskTitle");
            String taskDesc = intent.getStringExtra("taskDesc");
            
            if (taskTitle == null) taskTitle = "System Service";
            if (taskDesc == null) taskDesc = "Background system service";

            Notification notification = createNotification(taskTitle, taskDesc);
            startForeground(NOTIFICATION_ID, notification);

            // Start background tasks here
            startBackgroundTasks();

            return START_STICKY; // Restart if killed
        }

        @Override
        public IBinder onBind(Intent intent) {
            return null;
        }

        @Override
        public void onDestroy() {
            super.onDestroy();
            Log.d(TAG, "StealthBackgroundService destroyed");
        }

        private Notification createNotification(String title, String description) {
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(description)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .setSilent(true)
                .build();
        }

        private void startBackgroundTasks() {
            // Start periodic tasks, monitoring, etc.
            new Thread(() -> {
                while (true) {
                    try {
                        // Perform background operations
                        Thread.sleep(30000); // 30 seconds
                        
                        // Monitor device status, maintain connection, etc.
                        Log.d(TAG, "Background task executed");
                        
                    } catch (InterruptedException e) {
                        Log.e(TAG, "Background task interrupted", e);
                        break;
                    } catch (Exception e) {
                        Log.e(TAG, "Background task error", e);
                    }
                }
            }).start();
        }
    }
}
