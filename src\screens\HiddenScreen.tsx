import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  DeviceEventEmitter,
  StatusBar,
  Dimensions,
} from 'react-native';
import StealthService from '../services/StealthService';
import RemoteControlService from '../services/RemoteControlService';

const {width, height} = Dimensions.get('window');

const HiddenScreen: React.FC = () => {
  const [deviceStatus, setDeviceStatus] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
  const [lastCommand, setLastCommand] = useState<string>('None');

  useEffect(() => {
    setupEventListeners();
    loadInitialStatus();

    return () => {
      // Cleanup listeners
    };
  }, []);

  const setupEventListeners = () => {
    // Listen for device status updates
    const statusListener = DeviceEventEmitter.addListener(
      'deviceStatusUpdate',
      (status) => {
        setDeviceStatus(status);
      }
    );

    // Listen for command responses
    const commandListener = DeviceEventEmitter.addListener(
      'commandResponse',
      (response) => {
        setLastCommand(response.type || 'Unknown');
      }
    );

    // Listen for connection status
    const connectionListener = DeviceEventEmitter.addListener(
      'connectionStatus',
      (status) => {
        setConnectionStatus(status);
      }
    );

    return () => {
      statusListener.remove();
      commandListener.remove();
      connectionListener.remove();
    };
  };

  const loadInitialStatus = async () => {
    try {
      const status = await StealthService.getDeviceStatus();
      if (status) {
        setDeviceStatus(status);
      }
    } catch (error) {
      console.error('Failed to load initial status:', error);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return '#00FF00';
      case 'disconnected':
        return '#FF0000';
      case 'connecting':
        return '#FFFF00';
      default:
        return '#CCCCCC';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch {
      return 'Unknown';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Minimal status indicator - barely visible */}
      <View style={styles.statusContainer}>
        <View style={[styles.statusDot, {backgroundColor: getConnectionStatusColor()}]} />
        <Text style={styles.statusText}>
          {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
        </Text>
      </View>

      {/* Hidden mode indicator */}
      <View style={styles.centerContainer}>
        <Text style={styles.hiddenText}>●</Text>
        <Text style={styles.modeText}>Stealth Mode Active</Text>
      </View>

      {/* Device status (minimal) */}
      {deviceStatus && (
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            Battery: {Math.round((deviceStatus.batteryLevel || 0) * 100)}%
          </Text>
          <Text style={styles.infoText}>
            Last Update: {formatTimestamp(deviceStatus.timestamp)}
          </Text>
          <Text style={styles.infoText}>
            Last Command: {lastCommand}
          </Text>
        </View>
      )}

      {/* Emergency access hint (very subtle) */}
      <View style={styles.emergencyContainer}>
        <Text style={styles.emergencyText}>
          Emergency: Tap corners 5x each
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  statusContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    opacity: 0.3,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 5,
  },
  statusText: {
    color: '#333333',
    fontSize: 8,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  hiddenText: {
    color: '#111111',
    fontSize: 40,
    marginBottom: 10,
  },
  modeText: {
    color: '#222222',
    fontSize: 12,
    opacity: 0.5,
  },
  infoContainer: {
    position: 'absolute',
    bottom: 50,
    left: 10,
    opacity: 0.2,
  },
  infoText: {
    color: '#333333',
    fontSize: 8,
    marginBottom: 2,
  },
  emergencyContainer: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    alignItems: 'center',
    opacity: 0.1,
  },
  emergencyText: {
    color: '#222222',
    fontSize: 6,
  },
});

export default HiddenScreen;
