{"version": "0.2.0", "configurations": [{"name": "Debug Android", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "launch", "platform": "android"}, {"name": "Debug iOS", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "launch", "platform": "ios"}, {"name": "Attach to packager", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "attach"}, {"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/server.js", "cwd": "${workspaceFolder}/server", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}]}