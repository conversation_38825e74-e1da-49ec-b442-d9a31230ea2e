{"folders": [{"name": "🔒 Stealth Remote App", "path": "."}, {"name": "📱 Mobile App", "path": "./src"}, {"name": "🤖 Android Native", "path": "./android"}, {"name": "🖥️ Server", "path": "./server"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.exclude": {"**/node_modules": true, "**/android/build": true, "**/android/app/build": true, "**/.git": true}, "search.exclude": {"**/node_modules": true, "**/android/build": true, "**/android/app/build": true}}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🚀 Quick Start - Build & Run Everything", "type": "shell", "command": "${workspaceFolder}/quick-start.bat", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "🔨 Build Android APK", "type": "shell", "command": "${workspaceFolder}/build-and-install.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "🖥️ Start Server", "type": "shell", "command": "${workspaceFolder}/start-server.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🤖 Debug Android App", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "launch", "platform": "android"}, {"name": "🖥️ Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/server.js", "cwd": "${workspaceFolder}/server", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}]}, "extensions": {"recommendations": ["ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "msjsdiag.vscode-react-native", "redhat.java", "vscjava.vscode-java-pack"]}}