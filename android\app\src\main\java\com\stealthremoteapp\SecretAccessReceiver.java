package com.stealthremoteapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.telephony.SmsMessage;
import android.util.Log;

public class SecretAccessReceiver extends BroadcastReceiver {
    private static final String TAG = "SecretAccessReceiver";
    private static final String SECRET_CODE = "STEALTH_ACCESS_2024";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "Received action: " + action);

        if ("android.provider.Telephony.SMS_RECEIVED".equals(action)) {
            handleSmsReceived(context, intent);
        }
    }

    private void handleSmsReceived(Context context, Intent intent) {
        try {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                Object[] pdus = (Object[]) bundle.get("pdus");
                if (pdus != null) {
                    for (Object pdu : pdus) {
                        SmsMessage smsMessage = SmsMessage.createFromPdu((byte[]) pdu);
                        String messageBody = smsMessage.getMessageBody();
                        String sender = smsMessage.getOriginatingAddress();

                        Log.d(TAG, "SMS received from: " + sender + ", Message: " + messageBody);

                        if (messageBody != null && messageBody.contains(SECRET_CODE)) {
                            Log.d(TAG, "Secret access code detected!");
                            launchHiddenApp(context);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS", e);
        }
    }

    private void launchHiddenApp(Context context) {
        try {
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            launchIntent.putExtra("secret_access", true);
            
            context.startActivity(launchIntent);
            Log.d(TAG, "Hidden app launched via secret access");
        } catch (Exception e) {
            Log.e(TAG, "Failed to launch hidden app", e);
        }
    }
}
