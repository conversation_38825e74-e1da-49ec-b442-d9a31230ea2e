{"name": "@types/node", "version": "24.1.0", "description": "TypeScript definitions for node", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "githubUsername": "Microsoft", "url": "https://github.com/Microsoft"}, {"name": "<PERSON>", "githubUsername": "jkomyno", "url": "https://github.com/jkomyno"}, {"name": "<PERSON>", "githubUsername": "r3nya", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "githubUsername": "btoueg", "url": "https://github.com/btoueg"}, {"name": "<PERSON>", "githubUsername": "touffy", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mohsen1", "url": "https://github.com/mohsen1"}, {"name": "<PERSON><PERSON>", "githubUsername": "galkin", "url": "https://github.com/galkin"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "WilcoBakker", "url": "https://github.com/WilcoBakker"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "trivikr", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON>ny", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "addaleax", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "githubUsername": "victor<PERSON>in", "url": "https://github.com/victorperin"}, {"name": "NodeJS Contributors", "githubUsername": "NodeJS", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "githubUsername": "wafuwafu13", "url": "https://github.com/wafuwafu13"}, {"name": "<PERSON>", "githubUsername": "mcollina", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "githubUsername": "Semigradsky", "url": "https://github.com/Semigradsky"}, {"name": "<PERSON>", "githubUsername": "Renegade334", "url": "https://github.com/Renegade334"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "anon<PERSON>", "url": "https://github.com/anonrig"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=5.1": {"*": ["ts5.1/*"]}, "<=5.6": {"*": ["ts5.6/*"]}, "<=5.7": {"*": ["ts5.7/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {"undici-types": "~7.8.0"}, "peerDependencies": {}, "typesPublisherContentHash": "83cb68186fcf703a4b7951de645e523dc4495aa83f0ef95d9b64cb5032c5b1d6", "typeScriptVersion": "5.1"}