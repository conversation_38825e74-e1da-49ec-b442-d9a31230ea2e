import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  ScrollView,
  StatusBar,
} from 'react-native';

interface SetupScreenProps {
  onSetupComplete: (mode: 'target' | 'controller') => void;
}

const SetupScreen: React.FC<SetupScreenProps> = ({onSetupComplete}) => {
  const [selectedMode, setSelectedMode] = useState<'target' | 'controller' | null>(null);
  const [serverUrl, setServerUrl] = useState('ws://********:3000');
  const [accessCode, setAccessCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleModeSelection = (mode: 'target' | 'controller') => {
    setSelectedMode(mode);
  };

  const handleSetup = async () => {
    if (!selectedMode) {
      Alert.alert('Error', 'Please select a mode');
      return;
    }

    if (!serverUrl.trim()) {
      Alert.alert('Error', 'Please enter a server URL');
      return;
    }

    if (selectedMode === 'controller' && !accessCode.trim()) {
      Alert.alert('Error', 'Please enter an access code');
      return;
    }

    setIsLoading(true);

    try {
      // Validate server connection here if needed
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate setup

      if (selectedMode === 'target') {
        Alert.alert(
          'Target Mode Setup',
          'This app will be hidden after setup. Make sure you have the controller app ready on another device.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => setIsLoading(false),
            },
            {
              text: 'Continue',
              onPress: () => {
                onSetupComplete(selectedMode);
              },
            },
          ]
        );
      } else {
        onSetupComplete(selectedMode);
      }
    } catch (error) {
      Alert.alert('Error', 'Setup failed. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Remote Access Setup</Text>
          <Text style={styles.subtitle}>Choose your device mode</Text>
        </View>

        <View style={styles.modeContainer}>
          <TouchableOpacity
            style={[
              styles.modeButton,
              selectedMode === 'target' && styles.selectedMode,
            ]}
            onPress={() => handleModeSelection('target')}>
            <Text style={styles.modeTitle}>Target Device</Text>
            <Text style={styles.modeDescription}>
              This device will be controlled remotely. The app will be hidden after setup.
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.modeButton,
              selectedMode === 'controller' && styles.selectedMode,
            ]}
            onPress={() => handleModeSelection('controller')}>
            <Text style={styles.modeTitle}>Controller Device</Text>
            <Text style={styles.modeDescription}>
              Use this device to control other target devices remotely.
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.configContainer}>
          <Text style={styles.configLabel}>Server URL</Text>
          <TextInput
            style={styles.input}
            value={serverUrl}
            onChangeText={setServerUrl}
            placeholder="ws://********:3000"
            placeholderTextColor="#666"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {selectedMode === 'controller' && (
            <>
              <Text style={styles.configLabel}>Access Code</Text>
              <TextInput
                style={styles.input}
                value={accessCode}
                onChangeText={setAccessCode}
                placeholder="Enter access code"
                placeholderTextColor="#666"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </>
          )}
        </View>

        <TouchableOpacity
          style={[styles.setupButton, isLoading && styles.disabledButton]}
          onPress={handleSetup}
          disabled={isLoading}>
          <Text style={styles.setupButtonText}>
            {isLoading ? 'Setting up...' : 'Complete Setup'}
          </Text>
        </TouchableOpacity>

        <View style={styles.warningContainer}>
          <Text style={styles.warningText}>
            ⚠️ Important: This app is for authorized use only. Ensure you have proper permissions before installing on any device.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#CCCCCC',
  },
  modeContainer: {
    marginBottom: 30,
  },
  modeButton: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#333333',
  },
  selectedMode: {
    borderColor: '#007AFF',
    backgroundColor: '#001122',
  },
  modeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  modeDescription: {
    fontSize: 14,
    color: '#CCCCCC',
    lineHeight: 20,
  },
  configContainer: {
    marginBottom: 30,
  },
  configLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
  },
  input: {
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    padding: 15,
    color: '#FFFFFF',
    fontSize: 16,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#333333',
  },
  setupButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginBottom: 20,
  },
  disabledButton: {
    backgroundColor: '#555555',
  },
  setupButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  warningContainer: {
    backgroundColor: '#2A1A00',
    borderRadius: 8,
    padding: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9500',
  },
  warningText: {
    color: '#FF9500',
    fontSize: 12,
    lineHeight: 18,
  },
});

export default SetupScreen;
