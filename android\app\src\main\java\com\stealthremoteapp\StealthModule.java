package com.stealthremoteapp;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

import java.util.List;

public class StealthModule extends ReactContextBaseJavaModule {
    private static final String TAG = "StealthModule";
    private ReactApplicationContext reactContext;

    public StealthModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return "StealthModule";
    }

    @ReactMethod
    public void hideFromLauncher(Promise promise) {
        try {
            PackageManager packageManager = reactContext.getPackageManager();
            ComponentName componentName = new ComponentName(
                reactContext.getPackageName(),
                reactContext.getPackageName() + ".MainActivity"
            );

            // Disable the main activity to hide from launcher
            packageManager.setComponentEnabledSetting(
                componentName,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP
            );

            Log.d(TAG, "App hidden from launcher successfully");
            promise.resolve("App hidden from launcher");
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide app from launcher", e);
            promise.reject("HIDE_ERROR", "Failed to hide app from launcher: " + e.getMessage());
        }
    }

    @ReactMethod
    public void showInLauncher(Promise promise) {
        try {
            PackageManager packageManager = reactContext.getPackageManager();
            ComponentName componentName = new ComponentName(
                reactContext.getPackageName(),
                reactContext.getPackageName() + ".MainActivity"
            );

            // Re-enable the main activity to show in launcher
            packageManager.setComponentEnabledSetting(
                componentName,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP
            );

            Log.d(TAG, "App shown in launcher successfully");
            promise.resolve("App shown in launcher");
        } catch (Exception e) {
            Log.e(TAG, "Failed to show app in launcher", e);
            promise.reject("SHOW_ERROR", "Failed to show app in launcher: " + e.getMessage());
        }
    }

    @ReactMethod
    public void hideFromRecents(Promise promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ActivityManager activityManager = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
                if (activityManager != null) {
                    List<ActivityManager.AppTask> appTasks = activityManager.getAppTasks();
                    for (ActivityManager.AppTask appTask : appTasks) {
                        appTask.finishAndRemoveTask();
                    }
                }
            }
            
            Log.d(TAG, "App hidden from recents successfully");
            promise.resolve("App hidden from recents");
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide app from recents", e);
            promise.reject("HIDE_RECENTS_ERROR", "Failed to hide app from recents: " + e.getMessage());
        }
    }

    @ReactMethod
    public void isHiddenFromLauncher(Promise promise) {
        try {
            PackageManager packageManager = reactContext.getPackageManager();
            ComponentName componentName = new ComponentName(
                reactContext.getPackageName(),
                reactContext.getPackageName() + ".MainActivity"
            );

            int enabledSetting = packageManager.getComponentEnabledSetting(componentName);
            boolean isHidden = enabledSetting == PackageManager.COMPONENT_ENABLED_STATE_DISABLED;
            
            promise.resolve(isHidden);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check if app is hidden", e);
            promise.reject("CHECK_ERROR", "Failed to check if app is hidden: " + e.getMessage());
        }
    }

    @ReactMethod
    public void createSecretLauncher(String secretCode, Promise promise) {
        try {
            // This would create a secret way to launch the app
            // For example, through a specific phone number dial or SMS
            // Implementation depends on specific requirements
            
            Log.d(TAG, "Secret launcher created with code: " + secretCode);
            promise.resolve("Secret launcher created");
        } catch (Exception e) {
            Log.e(TAG, "Failed to create secret launcher", e);
            promise.reject("SECRET_ERROR", "Failed to create secret launcher: " + e.getMessage());
        }
    }

    @ReactMethod
    public void enableStealthMode(Promise promise) {
        try {
            // Comprehensive stealth mode activation
            hideFromLauncher(new Promise() {
                @Override
                public void resolve(Object value) {
                    Log.d(TAG, "Stealth mode enabled successfully");
                    promise.resolve("Stealth mode enabled");
                }

                @Override
                public void reject(String code, String message) {
                    promise.reject(code, message);
                }

                @Override
                public void reject(String code, String message, Throwable throwable) {
                    promise.reject(code, message, throwable);
                }

                @Override
                public void reject(String code, Throwable throwable) {
                    promise.reject(code, throwable);
                }

                @Override
                public void reject(Throwable throwable) {
                    promise.reject(throwable);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable stealth mode", e);
            promise.reject("STEALTH_ERROR", "Failed to enable stealth mode: " + e.getMessage());
        }
    }

    @ReactMethod
    public void disableStealthMode(Promise promise) {
        try {
            // Disable stealth mode and restore normal functionality
            showInLauncher(new Promise() {
                @Override
                public void resolve(Object value) {
                    Log.d(TAG, "Stealth mode disabled successfully");
                    promise.resolve("Stealth mode disabled");
                }

                @Override
                public void reject(String code, String message) {
                    promise.reject(code, message);
                }

                @Override
                public void reject(String code, String message, Throwable throwable) {
                    promise.reject(code, message, throwable);
                }

                @Override
                public void reject(String code, Throwable throwable) {
                    promise.reject(code, throwable);
                }

                @Override
                public void reject(Throwable throwable) {
                    promise.reject(throwable);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to disable stealth mode", e);
            promise.reject("STEALTH_DISABLE_ERROR", "Failed to disable stealth mode: " + e.getMessage());
        }
    }
}
