@echo off
echo ========================================
echo      Stealth Remote Server Startup
echo ========================================
echo.

:: Get computer's IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    set IP=!IP: =!
    goto :found_ip
)
:found_ip

cd server
echo 📦 Installing server dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Error installing server dependencies!
    pause
    exit /b 1
)

echo.
echo ========================================
echo           SERVER STARTING...
echo ========================================
echo.
echo 🖥️ Local Access: http://localhost:3000
echo 📱 Mobile Access: http://%IP%:3000/mobile
echo 🔗 WebSocket: ws://%IP%:3000
echo.
echo 📋 Mobile Setup URLs:
echo    Main Interface: http://%IP%:3000/mobile
echo    Download Guide: http://%IP%:3000/download
echo    Target Setup: http://%IP%:3000/setup-target
echo    Controller Setup: http://%IP%:3000/setup-controller
echo.
echo ⚠️ Keep this window open while using the app!
echo.
echo ========================================
call npm start
